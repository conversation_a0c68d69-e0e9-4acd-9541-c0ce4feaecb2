\n//\n//  FilamentMaterialManager.mm\n//  iOS3DRenderUI\n//\n//  专门处理纹理材质的管理器实现\n//\n\n#import \"FilamentMaterialManager.h\"\n\n// 包含 Filament C++ 头文件\n#include <filament/Engine.h>\n#include <filament/Material.h>\n#include <filament/MaterialInstance.h>\n#include <filament/Texture.h>\n#include <filament/TextureSampler.h>\n#include <backend/PixelBufferDescriptor.h>\n#include <math/vec3.h>\n#include <math/vec2.h>\n\nusing namespace filament;\n\n@interface FilamentMaterialManager () {\n    Engine* _engine;\n    Material* _texturedMaterial;\n    MaterialInstance* _currentMaterialInstance;\n}\n@end\n\n@implementation FilamentMaterialManager\n\n- (instancetype)initWithEngine:(void*)engine {\n    self = [super init];\n    if (self) {\n        _engine = (Engine*)engine;\n        _texturedMaterial = nullptr;\n        _currentMaterialInstance = nullptr;\n    }\n    return self;\n}\n\n- (void)dealloc {\n    if (_engine) {\n        if (_currentMaterialInstance) {\n            _engine->destroy(_currentMaterialInstance);\n        }\n        if (_texturedMaterial) {\n            _engine->destroy(_texturedMaterial);\n        }\n    }\n}\n\n#pragma mark - 材质创建和管理\n\n- (void*)createTexturedMaterial {\n    NSLog(@\"🔧 开始创建纹理支持材质...\");\n    \n    // 方案1: 尝试加载预编译材质\n    if ([self loadPrecompiledMaterial:@\"textured_pbr\"]) {\n        NSLog(@\"✅ 成功加载预编译纹理材质\");\n        return _currentMaterialInstance;\n    }\n    \n    // 方案2: 创建运行时材质\n    NSLog(@\"⚠️ 预编译材质不可用，尝试创建运行时材质...\");\n    MaterialInstance* runtimeMaterial = [self createRuntimeTexturedMaterial];\n    if (runtimeMaterial) {\n        _currentMaterialInstance = runtimeMaterial;\n        NSLog(@\"✅ 成功创建运行时纹理材质\");\n        return _currentMaterialInstance;\n    }\n    \n    // 方案3: 降级到默认材质\n    NSLog(@\"⚠️ 降级到默认材质\");\n    return [self getFallbackMaterial];\n}\n\n- (BOOL)loadPrecompiledMaterial:(NSString*)materialName {\n    // 查找预编译的材质文件\n    NSString *materialPath = [[NSBundle mainBundle] pathForResource:materialName \n                                                             ofType:@\"filamat\" \n                                                        inDirectory:@\"Materials\"];\n    if (!materialPath) {\n        NSLog(@\"⚠️ 未找到预编译材质文件: %@.filamat\", materialName);\n        return NO;\n    }\n    \n    NSData *materialData = [NSData dataWithContentsOfFile:materialPath];\n    if (!materialData || materialData.length == 0) {\n        NSLog(@\"❌ 无法读取材质文件或文件为空\");\n        return NO;\n    }\n    \n    try {\n        // 从二进制数据创建材质\n        _texturedMaterial = Material::Builder()\n            .package(materialData.bytes, materialData.length)\n            .build(*_engine);\n            \n        if (!_texturedMaterial) {\n            NSLog(@\"❌ 材质创建失败\");\n            return NO;\n        }\n        \n        // 创建材质实例\n        _currentMaterialInstance = _texturedMaterial->createInstance();\n        if (!_currentMaterialInstance) {\n            NSLog(@\"❌ 材质实例创建失败\");\n            return NO;\n        }\n        \n        NSLog(@\"✅ 预编译材质加载成功: %s\", _texturedMaterial->getName());\n        \n        // 验证材质支持的参数\n        [self logMaterialParameters:_texturedMaterial];\n        \n        return YES;\n        \n    } catch (const std::exception& e) {\n        NSLog(@\"❌ 加载预编译材质时发生异常: %s\", e.what());\n        return NO;\n    } catch (...) {\n        NSLog(@\"❌ 加载预编译材质时发生未知异常\");\n        return NO;\n    }\n}\n\n- (void*)getFallbackMaterial {\n    if (_currentMaterialInstance) {\n        return _currentMaterialInstance;\n    }\n    \n    // 创建默认材质实例\n    _currentMaterialInstance = _engine->getDefaultMaterial()->createInstance();\n    NSLog(@\"⚠️ 使用默认材质作为降级方案\");\n    return _currentMaterialInstance;\n}\n\n#pragma mark - 运行时材质创建\n\n- (MaterialInstance*)createRuntimeTexturedMaterial {\n    NSLog(@\"🛠️ 尝试创建运行时纹理材质...\");\n    \n    try {\n        // 创建基于源码的材质\n        const char* materialSource = R\"(\n            material {\n                name : RuntimeTextured,\n                shadingModel : lit,\n                blending : opaque,\n                parameters : [\n                    {\n                        type : sampler2d,\n                        name : baseColorMap\n                    },\n                    {\n                        type : float3,\n                        name : baseColorFactor,\n                        default : [1.0, 1.0, 1.0]\n                    },\n                    {\n                        type : float,\n                        name : metallicFactor,\n                        default : 0.0\n                    },\n                    {\n                        type : float,\n                        name : roughnessFactor,\n                        default : 0.5\n                    }\n                ]\n            }\n            \n            fragment {\n                void material(inout MaterialInputs material) {\n                    prepareMaterial(material);\n                    float4 baseColor = texture(materialParams_baseColorMap, getUV0());\n                    material.baseColor = baseColor * float4(materialParams.baseColorFactor, 1.0);\n                    material.metallic = materialParams.metallicFactor;\n                    material.roughness = materialParams.roughnessFactor;\n                }\n            }\n        )\";\n        \n        // 注意：这种方法需要材质编译器在运行时可用\n        // 在实际的 iOS 应用中，这通常不可行\n        NSLog(@\"⚠️ 运行时材质编译在移动设备上不支持\");\n        return nullptr;\n        \n    } catch (...) {\n        NSLog(@\"❌ 运行时材质创建失败\");\n        return nullptr;\n    }\n}\n\n#pragma mark - 纹理应用\n\n- (BOOL)applyTexture:(void*)texture toMaterial:(void*)materialInstance {\n    if (!texture || !materialInstance) {\n        NSLog(@\"❌ 纹理或材质实例为空\");\n        return NO;\n    }\n    \n    Texture* filamentTexture = (Texture*)texture;\n    MaterialInstance* matInstance = (MaterialInstance*)materialInstance;\n    \n    NSLog(@\"🎨 开始应用纹理到材质...\");\n    \n    try {\n        // 创建纹理采样器\n        TextureSampler sampler = TextureSampler(\n            TextureSampler::MinFilter::LINEAR,\n            TextureSampler::MagFilter::LINEAR,\n            TextureSampler::WrapMode::REPEAT\n        );\n        \n        // 尝试应用纹理到 baseColorMap 参数\n        const Material* material = matInstance->getMaterial();\n        \n        if (material->hasParameter(\"baseColorMap\")) {\n            matInstance->setParameter(\"baseColorMap\", filamentTexture, sampler);\n            NSLog(@\"✅ 成功设置 baseColorMap 纹理参数\");\n            \n            // 设置基础颜色因子为白色，确保纹理完全显示\n            if (material->hasParameter(\"baseColorFactor\")) {\n                math::float3 whiteColor = {1.0f, 1.0f, 1.0f};\n                matInstance->setParameter(\"baseColorFactor\", whiteColor);\n                NSLog(@\"✅ 设置 baseColorFactor 为白色\");\n            }\n            \n            return YES;\n        } else {\n            NSLog(@\"❌ 材质不支持 baseColorMap 参数\");\n            return NO;\n        }\n        \n    } catch (const std::exception& e) {\n        NSLog(@\"❌ 应用纹理时发生异常: %s\", e.what());\n        return NO;\n    } catch (...) {\n        NSLog(@\"❌ 应用纹理时发生未知异常\");\n        return NO;\n    }\n}\n\n- (BOOL)setTextureParameters:(void*)materialInstance \n                     texture:(void*)texture\n                      offset:(CGPoint)offset\n                       scale:(float)scale {\n    if (!materialInstance) {\n        return NO;\n    }\n    \n    MaterialInstance* matInstance = (MaterialInstance*)materialInstance;\n    const Material* material = matInstance->getMaterial();\n    \n    try {\n        // 设置纹理偏移\n        if (material->hasParameter(\"textureOffset\")) {\n            math::float2 textureOffset = {(float)offset.x, (float)offset.y};\
            matInstance->setParameter("textureOffset", textureOffset);
            NSLog(@"✅ 设置纹理偏移: (%.3f, %.3f)", offset.x, offset.y);
        }
        
        // 设置纹理缩放
        if (material->hasParameter("textureScale")) {
            matInstance->setParameter("textureScale", scale);
            NSLog(@"✅ 设置纹理缩放: %.3f", scale);
        }
        
        return YES;
        
    } catch (const std::exception& e) {
        NSLog(@"❌ 设置纹理参数时发生异常: %s", e.what());
        return NO;
    }
}

#pragma mark - 材质参数设置

- (BOOL)setMaterialColor:(void*)materialInstance color:(UIColor*)color {
    if (!materialInstance || !color) {
        return NO;
    }
    
    MaterialInstance* matInstance = (MaterialInstance*)materialInstance;
    const Material* material = matInstance->getMaterial();
    
    // 提取 UIColor 的 RGB 分量
    CGFloat red, green, blue, alpha;
    [color getRed:&red green:&green blue:&blue alpha:&alpha];
    
    math::float3 colorVec = {(float)red, (float)green, (float)blue};
    
    try {
        if (material->hasParameter("baseColorFactor")) {
            matInstance->setParameter("baseColorFactor", colorVec);
            NSLog(@"✅ 设置材质颜色: (%.3f, %.3f, %.3f)", red, green, blue);
            return YES;
        } else if (material->hasParameter("baseColor")) {
            matInstance->setParameter("baseColor", RgbType::sRGB, colorVec);
            NSLog(@"✅ 设置材质 baseColor: (%.3f, %.3f, %.3f)", red, green, blue);
            return YES;
        } else {
            NSLog(@"⚠️ 材质不支持颜色参数");
            return NO;
        }
        
    } catch (const std::exception& e) {
        NSLog(@"❌ 设置材质颜色时发生异常: %s", e.what());
        return NO;
    }
}

- (BOOL)setMaterialPBRParameters:(void*)materialInstance 
                        metallic:(float)metallic 
                       roughness:(float)roughness {
    if (!materialInstance) {
        return NO;
    }
    
    MaterialInstance* matInstance = (MaterialInstance*)materialInstance;
    const Material* material = matInstance->getMaterial();
    
    try {
        BOOL success = NO;
        
        if (material->hasParameter("metallicFactor")) {
            matInstance->setParameter("metallicFactor", metallic);
            NSLog(@"✅ 设置金属度: %.3f", metallic);
            success = YES;
        }
        
        if (material->hasParameter("roughnessFactor")) {
            matInstance->setParameter("roughnessFactor", roughness);
            NSLog(@"✅ 设置粗糙度: %.3f", roughness);
            success = YES;
        }
        
        return success;
        
    } catch (const std::exception& e) {
        NSLog(@"❌ 设置 PBR 参数时发生异常: %s", e.what());
        return NO;
    }
}

#pragma mark - 辅助方法

- (void)logMaterialParameters:(Material*)material {
    if (!material) return;
    
    NSLog(@"📋 材质参数信息:");
    NSLog(@"   材质名称: %s", material->getName());
    
    // 检查常见的纹理参数
    NSArray* textureParams = @[@"baseColorMap", @"diffuseMap", @"albedoMap", @"colorMap"];
    NSArray* colorParams = @[@"baseColorFactor", @"baseColor", @"color"];
    NSArray* pbrParams = @[@"metallicFactor", @"roughnessFactor", @"emissiveFactor"];
    NSArray* transformParams = @[@"textureOffset", @"textureScale"];
    
    NSLog(@"   纹理参数:");
    for (NSString* param in textureParams) {
        const char* cParam = [param UTF8String];
        if (material->hasParameter(cParam)) {
            NSLog(@"     ✅ %@", param);
        }
    }
    
    NSLog(@"   颜色参数:");
    for (NSString* param in colorParams) {
        const char* cParam = [param UTF8String];
        if (material->hasParameter(cParam)) {
            NSLog(@"     ✅ %@", param);
        }
    }
    
    NSLog(@"   PBR参数:");
    for (NSString* param in pbrParams) {
        const char* cParam = [param UTF8String];
        if (material->hasParameter(cParam)) {
            NSLog(@"     ✅ %@", param);
        }
    }
    
    NSLog(@"   变换参数:");
    for (NSString* param in transformParams) {
        const char* cParam = [param UTF8String];
        if (material->hasParameter(cParam)) {
            NSLog(@"     ✅ %@", param);
        }
    }
}

@end