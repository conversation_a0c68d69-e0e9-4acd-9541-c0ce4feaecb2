//
//  FilamentWrapper.mm
//  iOS3DRenderUI
//
//  Filament C++ 包装器实现
//

#import "FilamentWrapper.h"

// 包含 C++ 标准库头文件
#include <vector>

#import <CoreGraphics/CoreGraphics.h>
#import <UIKit/UIKit.h>

// 包含 Filament C++ 头文件
#include <filament/Engine.h>
#include <filament/Renderer.h>
#include <filament/Scene.h>
#include <filament/View.h>
#include <filament/Camera.h>
#include <filament/SwapChain.h>
#include <filament/LightManager.h>
#include <filament/TransformManager.h>
#include <filament/RenderableManager.h>
#include <filament/Material.h>
#include <filament/MaterialInstance.h>
#include <filament/Texture.h>
#include <filament/Viewport.h>
#include <filament/VertexBuffer.h>
#include <filament/IndexBuffer.h>
#include <filament/Skybox.h>
#include <filament/IndirectLight.h>
#include <filament/Color.h>
#include <filament/TextureSampler.h>
#include <backend/PixelBufferDescriptor.h>
#include <math/vec3.h>
#include <math/vec2.h>
#include <gltfio/AssetLoader.h>
#include <gltfio/ResourceLoader.h>
#include <gltfio/FilamentAsset.h>
#include <utils/EntityManager.h>

using namespace filament;
using namespace utils;
using namespace gltfio;

@interface FilamentWrapper () {
    Engine* _engine;
    Renderer* _renderer;
    Scene* _scene;
    View* _view;
    Camera* _camera;
    SwapChain* _swapChain;
    
    // 实体
    Entity _cameraEntity;
    Entity _lightEntity;
    Entity _modelEntity;
    
    // 模型相关
    FilamentAsset* _currentAsset;
    
    // 内置几何体
    VertexBuffer* _sphereVertexBuffer;
    IndexBuffer* _sphereIndexBuffer;
    MaterialInstance* _sphereMaterial;
    
    // 球体顶点数据（用于更新颜色）
    struct SphereVertex {
        math::float3 position;
        math::float4 tangents;
        math::float2 uv;
        math::float4 color;
    };
    std::vector<SphereVertex> _sphereVertices;
    int _sphereVertexCount;
    
    // 纹理相关
    std::vector<Texture*> _textures;
    MaterialInstance* _currentMaterial;
    int _activeTextureIndex;
    math::float2 _textureOffset;
    
    // 动画
    NSTimer* _animationTimer;
}
@end

@implementation FilamentWrapper

- (instancetype)init {
    self = [super init];
    if (self) {
        // 初始化变量
        _engine = nullptr;
        _renderer = nullptr;
        _scene = nullptr;
        _view = nullptr;
        _camera = nullptr;
        _swapChain = nullptr;
        _currentAsset = nullptr;
        _currentMaterial = nullptr;
        
        // 初始化内置几何体变量
        _sphereVertexBuffer = nullptr;
        _sphereIndexBuffer = nullptr;
        _sphereMaterial = nullptr;
        _activeTextureIndex = 0;
        _textureOffset = {0.0f, 0.0f};
        _animationTimer = nil;
        
        // 创建 Filament 引擎
        _engine = Engine::create(Engine::Backend::METAL);
        if (!_engine) {
            NSLog(@"Failed to create Filament engine");
            return nil;
        }
        
        // 创建渲染器
        _renderer = _engine->createRenderer();
        
        // 创建场景
        _scene = _engine->createScene();
        
        // 创建相机
        _cameraEntity = _engine->getEntityManager().create();
        _camera = _engine->createCamera(_cameraEntity);
        
        // 创建视图
        _view = _engine->createView();
        _view->setScene(_scene);
        _view->setCamera(_camera);
        
        // 设置基本光照
        [self setupLighting];
        
        // 设置相机位置
        [self setupCamera];
    }
    return self;
}

- (void)dealloc {
    [self cleanup];
}

- (void)cleanup {
    if (_animationTimer) {
        [_animationTimer invalidate];
        _animationTimer = nil;
    }
    
    if (_engine) {
        [self removeCurrentModel];
        [self clearTextures];
        
        if (_currentMaterial) {
            _engine->destroy(_currentMaterial);
            _currentMaterial = nullptr;
        }
        
        if (_swapChain) {
            _engine->destroy(_swapChain);
            _swapChain = nullptr;
        }
        
        // 清理内置几何体
        if (_sphereVertexBuffer) {
            _engine->destroy(_sphereVertexBuffer);
            _sphereVertexBuffer = nullptr;
        }
        if (_sphereIndexBuffer) {
            _engine->destroy(_sphereIndexBuffer);
            _sphereIndexBuffer = nullptr;
        }
        if (_sphereMaterial) {
            _engine->destroy(_sphereMaterial);
            _sphereMaterial = nullptr;
        }
        
        _engine->destroyCameraComponent(_cameraEntity);
        _engine->destroy(_view);
        _engine->destroy(_scene);
        _engine->destroy(_renderer);
        
        Engine::destroy(_engine);
        _engine = nullptr;
    }
}

- (void)setupLighting {
    // 创建点光源，位于摄像机斜后上方60度
    _lightEntity = _engine->getEntityManager().create();
    
    // 计算光源位置：摄像机在 (0, 0, 8)，球体在 (0, 0, 0)
    // 斜后上方60度角度计算
    float cameraDistance = 8.0f;
    float lightDistance = cameraDistance * 0.8f; // 距离适中
    
    // 60度角：30度向上，30度向后偏移
    float angleRad = 60.0f * M_PI / 180.0f;
    float lightX = lightDistance * sin(angleRad) * 0.6f; // 向右偏移
    float lightY = lightDistance * sin(angleRad) * 0.8f; // 向上偏移  
    float lightZ = cameraDistance + lightDistance * cos(angleRad) * 0.5f; // 向后偏移
    
    math::float3 lightPosition = {lightX, lightY, lightZ};
    
    LightManager::Builder(LightManager::Type::POINT)
        .color(Color::toLinear<ACCURATE>({1.0f, 1.0f, 1.0f}))
        .intensity(80000.0f) // 点光源适中强度
        .position(lightPosition)
        .falloff(15.0f) // 光衰减半径
        .castShadows(false)
        .build(*_engine, _lightEntity);
    
    // 添加到场景
    _scene->addEntity(_lightEntity);
    
    NSLog(@"✨ 设置点光源完成，位置: (%.2f, %.2f, %.2f)", lightX, lightY, lightZ);
    
    // 添加柔和的环境光以确保球体整体可见
    auto skybox = Skybox::Builder()
        .color({0.1f, 0.1f, 0.15f, 1.0f}) // 较暗的环境色，突出点光源
        .build(*_engine);
    _scene->setSkybox(skybox);
    
    // 设置间接光照
    auto ibl = IndirectLight::Builder()
        .intensity(20000.0f) // 降低环境光，突出点光源效果
        .build(*_engine);
    _scene->setIndirectLight(ibl);
    
    NSLog(@"🌅 环境光照设置完成，强度已调低突出点光源效果");
}

- (void)setupCamera {
    // 设置相机变换
    auto& tcm = _engine->getTransformManager();
    // 优化相机位置：球体半径2.0，距离8.0，使球体占视野约50%
    math::float3 eye = {0, 0, 8};    // 适当距离，确保球体完整显示
    math::float3 center = {0, 0, 0}; // 看向原点（球体中心）
    math::float3 up = {0, 1, 0};     // Y轴向上
    
    math::float3 zAxis = normalize(eye - center);
    math::float3 xAxis = normalize(cross(up, zAxis));
    math::float3 yAxis = cross(zAxis, xAxis);
    
    math::mat4f transform = {
        math::float4(xAxis, 0),
        math::float4(yAxis, 0),
        math::float4(zAxis, 0),
        math::float4(eye, 1)
    };
    
    tcm.setTransform(tcm.getInstance(_cameraEntity), transform);
    
    // 设置投影矩阵
    const double aspect = 1.0; // 将在 resize 时更新
    const double fovInDegrees = 45.0;
    const double near = 0.1;
    const double far = 100.0;
    
    _camera->setProjection(fovInDegrees, aspect, near, far);
    
    // 计算视野信息用于调试
    double halfFovRad = (fovInDegrees * M_PI / 180.0) / 2.0;
    double visibleRadius = tan(halfFovRad) * 8.0; // 距离8.0时的可见半径
    NSLog(@"相机设置: 距离=8.0, 视野=%.1f°, 可见半径=%.2f, 球体半径=1.5", fovInDegrees, visibleRadius);
}

#pragma mark - 表面管理

- (BOOL)setupWithMetalLayer:(CAMetalLayer *)metalLayer {
    if (!_engine || !metalLayer) {
        return NO;
    }
    
    // 创建交换链
    _swapChain = _engine->createSwapChain((__bridge void*)metalLayer);
    return _swapChain != nullptr;
}

- (void)resizeWithWidth:(NSUInteger)width height:(NSUInteger)height {
    if (_view) {
        _view->setViewport({0, 0, (uint32_t)width, (uint32_t)height});
    }
    
    if (_camera && width > 0 && height > 0) {
        const double aspect = (double)width / (double)height;
        const double fovInDegrees = 45.0;
        const double near = 0.1;
        const double far = 100.0;
        
        _camera->setProjection(fovInDegrees, aspect, near, far);
    }
}

#pragma mark - 渲染

- (void)render {
    if (!_renderer || !_swapChain || !_view) {
        NSLog(@"渲染器、交换链或视图为空");
        return;
    }
    
    static int frameCount = 0;
    frameCount++;
    
    if (_renderer->beginFrame(_swapChain)) {
        _renderer->render(_view);
        _renderer->endFrame();
        
        // 每100帧打印一次调试信息
        if (frameCount % 100 == 0) {
            NSLog(@"已渲染 %d 帧，场景中实体数量: %zu", frameCount, _scene->getEntityCount());
        }
    }
}

#pragma mark - 模型加载

- (BOOL)loadModelFromData:(NSData *)data {
    if (!_engine || !data) {
        return NO;
    }
    
    // 移除旧模型
    [self removeCurrentModel];
    
    // 创建资产加载器
    AssetConfiguration config = {.engine = _engine, .materials = nullptr};
    AssetLoader* loader = AssetLoader::create(config);
    if (!loader) {
        return NO;
    }
    
    // 加载模型
    _currentAsset = loader->createAsset((const uint8_t*)data.bytes, (uint32_t)data.length);
    AssetLoader::destroy(&loader);
    
    if (!_currentAsset) {
        return NO;
    }
    
    // 创建资源加载器
    ResourceConfiguration resourceConfig = {.engine = _engine, .gltfPath = nullptr};
    ResourceLoader* resourceLoader = new ResourceLoader(resourceConfig);
    if (!resourceLoader->loadResources(_currentAsset)) {
        delete resourceLoader;
        return NO;
    }
    delete resourceLoader;
    
    // 添加到场景
    const Entity* entities = _currentAsset->getEntities();
    size_t entityCount = _currentAsset->getEntityCount();
    for (size_t i = 0; i < entityCount; ++i) {
        _scene->addEntity(entities[i]);
    }
    
    if (entityCount > 0) {
        _modelEntity = entities[0];
    }
    
    return YES;
}

- (void)removeCurrentModel {
    if (_currentAsset && _engine) {
        const Entity* entities = _currentAsset->getEntities();
        size_t entityCount = _currentAsset->getEntityCount();
        for (size_t i = 0; i < entityCount; ++i) {
            _scene->remove(entities[i]);
        }
        // 需要保留 AssetLoader 实例来销毁资产
        // 在实际应用中，应该将 AssetLoader 作为实例变量保存
        // 这里暂时注释掉，因为我们没有保存 AssetLoader 实例
        // _assetLoader->destroyAsset(_currentAsset);
        _currentAsset = nullptr;
        _modelEntity = {};
    }
}

- (BOOL)createDefaultSphere {
    if (!_engine) {
        return NO;
    }
    
    // 移除现有模型
    [self removeCurrentModel];
    
    // 球体参数
    const int segments = 32; // 经度分段数
    const int rings = 16;    // 纬度分段数
    const float radius = 1.5f; // 优化球体大小：配合相机距离8.0，占视野约37%
    
    // 计算顶点和索引数量
    const int vertexCount = (rings + 1) * (segments + 1);
    const int indexCount = rings * segments * 6;
    
    // 保存球体参数到类成员变量
    _sphereVertexCount = vertexCount;
    _sphereVertices.resize(vertexCount);
    
    std::vector<uint16_t> indices(indexCount);
    
    // 生成球体顶点
    int vertexIndex = 0;
    // 默认颜色：蓝色
    math::float4 defaultColor = {0.3f, 0.5f, 0.9f, 1.0f}; // 蓝色 RGB(77, 128, 230)
    
    for (int ring = 0; ring <= rings; ring++) {
        float phi = M_PI * ring / rings; // 纬度角
        float y = radius * cos(phi);
        float ringRadius = radius * sin(phi);
        
        for (int segment = 0; segment <= segments; segment++) {
            float theta = 2.0f * M_PI * segment / segments; // 经度角
            float x = ringRadius * cos(theta);
            float z = ringRadius * sin(theta);
            
            // 位置
            _sphereVertices[vertexIndex].position = {x, y, z};
            
            // 简化的切线空间（对于球体，我们可以使用简单的四元数）
            // 这里使用一个简化的切线四元数，让 Filament 处理法向量
            _sphereVertices[vertexIndex].tangents = {0.0f, 0.0f, 0.0f, 1.0f};
            
            // UV 坐标
            float u = (float)segment / segments;
            float v = (float)ring / rings;
            _sphereVertices[vertexIndex].uv = {u, v};
            
            // 顶点颜色 - 默认为白色，稍后可以通过更新缓冲区来改变
            _sphereVertices[vertexIndex].color = defaultColor;
            
            vertexIndex++;
        }
    }
    
    // 生成球体索引
    int indexIndex = 0;
    for (int ring = 0; ring < rings; ring++) {
        for (int segment = 0; segment < segments; segment++) {
            int current = ring * (segments + 1) + segment;
            int next = current + segments + 1;
            
            // 第一个三角形（逆时针）
            indices[indexIndex++] = current;
            indices[indexIndex++] = current + 1;
            indices[indexIndex++] = next;
            
            // 第二个三角形（逆时针）
            indices[indexIndex++] = current + 1;
            indices[indexIndex++] = next + 1;
            indices[indexIndex++] = next;
        }
    }
    
    // 创建顶点缓冲区 - 添加颜色属性
    _sphereVertexBuffer = VertexBuffer::Builder()
        .vertexCount(vertexCount)
        .bufferCount(1)
        .attribute(VertexAttribute::POSITION, 0, VertexBuffer::AttributeType::FLOAT3, 0, sizeof(SphereVertex))
        .attribute(VertexAttribute::TANGENTS, 0, VertexBuffer::AttributeType::FLOAT4, sizeof(math::float3), sizeof(SphereVertex))
        .attribute(VertexAttribute::UV0, 0, VertexBuffer::AttributeType::FLOAT2, sizeof(math::float3) + sizeof(math::float4), sizeof(SphereVertex))
        .attribute(VertexAttribute::COLOR, 0, VertexBuffer::AttributeType::FLOAT4, sizeof(math::float3) + sizeof(math::float4) + sizeof(math::float2), sizeof(SphereVertex))
        .build(*_engine);
    
    // 创建索引缓冲区
    _sphereIndexBuffer = IndexBuffer::Builder()
        .indexCount(indexCount)
        .bufferType(IndexBuffer::IndexType::USHORT)
        .build(*_engine);
    
    // 上传顶点数据
    _sphereVertexBuffer->setBufferAt(*_engine, 0,
        VertexBuffer::BufferDescriptor(_sphereVertices.data(), _sphereVertices.size() * sizeof(SphereVertex)));
    
    // 上传索引数据
    _sphereIndexBuffer->setBuffer(*_engine,
        IndexBuffer::BufferDescriptor(indices.data(), indices.size() * sizeof(uint16_t)));
    
    // 创建一个简单的有色材质来替代默认材质
    _sphereMaterial = [self createSimpleMaterial];
    
    NSLog(@"球体几何体创建完成: %d 顶点, %d 索引", vertexCount, indexCount);
    NSLog(@"使用自定义简单材质创建球体");
    
    // 创建实体和可渲染组件
    _modelEntity = _engine->getEntityManager().create();
    
    // 构建可渲染组件
    RenderableManager::Builder(1)
        .boundingBox({{-radius, -radius, -radius}, {radius, radius, radius}})
        .material(0, _sphereMaterial)
        .geometry(0, RenderableManager::PrimitiveType::TRIANGLES, _sphereVertexBuffer, _sphereIndexBuffer)
        .culling(false) // 禁用背面剔除，确保内部也可见
        .receiveShadows(false) // 暂时禁用阴影以简化调试
        .castShadows(false)
        .build(*_engine, _modelEntity);
    
    // 添加到场景
    _scene->addEntity(_modelEntity);
    
    NSLog(@"球体实体已添加到场景，实体ID: %d", _modelEntity.getId());
    NSLog(@"场景中实体数量: %zu", _scene->getEntityCount());
    
    return YES;
}

// 创建一个简单的材质，支持基本颜色和纹理
- (MaterialInstance*)createSimpleMaterial {
    // 方案1: 尝试加载预编译的自定义材质
    MaterialInstance* instance = [self loadCustomMaterial];
    if (instance) {
        NSLog(@"✅ 成功创建自定义纹理材质");
        return instance;
    }
    
    // 方案2: 创建一个运行时的简单材质（无纹理支持）
    NSLog(@"⚠️ 无法加载自定义材质，使用简化方案");
    
    // 使用默认材质，但优化参数设置
    try {
        instance = _engine->getDefaultMaterial()->createInstance();
        
        const Material* mat = instance->getMaterial();
        NSLog(@"使用默认材质: %s", mat->getName());
        
        // 检查并设置支持的参数
        std::vector<std::string> supportedParams;
        
        // 尝试设置基础颜色（更亮一些）
        math::float3 brightColor = {0.9f, 0.9f, 0.9f};
        
        if (mat->hasParameter("baseColor")) {
            instance->setParameter("baseColor", RgbType::sRGB, brightColor);
            supportedParams.push_back("baseColor");
        }
        
        if (mat->hasParameter("color")) {
            instance->setParameter("color", RgbType::sRGB, brightColor);
            supportedParams.push_back("color");
        }
        
        if (mat->hasParameter("baseColorFactor")) {
            instance->setParameter("baseColorFactor", 1.0f);
            supportedParams.push_back("baseColorFactor");
        }
        
        // 设置 PBR 参数使材质更加明亮
        if (mat->hasParameter("metallicFactor")) {
            instance->setParameter("metallicFactor", 0.0f); // 非金属
            supportedParams.push_back("metallicFactor");
        }
        
        if (mat->hasParameter("roughnessFactor")) {
            instance->setParameter("roughnessFactor", 0.5f); // 中等粗糙度
            supportedParams.push_back("roughnessFactor");
        }
        
        NSLog(@"默认材质支持的参数数量: %zu", supportedParams.size());
        for (const std::string& param : supportedParams) {
            NSLog(@"  - %s", param.c_str());
        }
        
        if (supportedParams.empty()) {
            NSLog(@"⚠️ 默认材质不支持任何颜色参数");
        }
        
    } catch (const std::exception& e) {
        NSLog(@"❌ 创建材质时发生异常: %s", e.what());
        instance = _engine->getDefaultMaterial()->createInstance();
    } catch (...) {
        NSLog(@"❌ 创建材质时发生未知异常");
        instance = _engine->getDefaultMaterial()->createInstance();
    }
    
    return instance;
}

// 尝试创建运行时的简单材质（替代预编译材质）
- (MaterialInstance*)loadCustomMaterial {
    NSLog(@"🔧 尝试创建支持颜色的材质...");
    
    // 策略1: 尝试创建一个基于源码的最简单 unlit 材质
    MaterialInstance* instance = [self createRuntimeUnlitMaterial];
    if (instance) {
        NSLog(@"✅ 成功创建运行时 unlit 材质");
        return instance;
    }
    
    // 策略2: 检查是否有其他可用的内置材质
    NSLog(@"🔄 尝试查找其他内置材质...");
    
    // 尝试默认材质，但深度检查所有参数
    try {
        instance = _engine->getDefaultMaterial()->createInstance();
        const Material* mat = instance->getMaterial();
        
        NSLog(@"检查材质: %s", mat->getName());
        
        // 完整检查所有可能的参数名
        NSArray* allPossibleParams = @[
            // 颜色参数
            @"baseColor", @"color", @"diffuseColor", @"albedo", @"tint", @"mainColor",
            // 因子参数  
            @"baseColorFactor", @"colorFactor", @"diffuseFactor", @"albedoFactor",
            // PBR 参数
            @"metallicFactor", @"roughnessFactor", @"emissiveFactor",
            // 纹理参数
            @"baseColorMap", @"diffuseMap", @"colorMap", @"albedoMap", @"mainTexture"
        ];
        
        NSMutableArray* supportedParams = [NSMutableArray array];
        
        for (NSString* paramName in allPossibleParams) {
            const char* cParamName = [paramName UTF8String];
            if (mat->hasParameter(cParamName)) {
                [supportedParams addObject:paramName];
            }
        }
        
        NSLog(@"🔍 检查完成，支持的参数: %@", supportedParams);
        
        if (supportedParams.count > 0) {
            NSLog(@"✅ 找到支持参数的材质！");
            return instance;
        } else {
            NSLog(@"❌ 默认材质不支持任何参数，需要使用顶点颜色方案");
            return instance; // 返回但标记为需要特殊处理
        }
        
    } catch (const std::exception& e) {
        NSLog(@"❌ 检查默认材质时发生异常: %s", e.what());
        return nullptr;
    } catch (...) {
        NSLog(@"❌ 检查默认材质时发生未知异常");
        return nullptr;
    }
}

// 创建最简单的运行时 unlit 材质
- (MaterialInstance*)createRuntimeUnlitMaterial {
    NSLog(@"🛠️ 尝试创建运行时 unlit 材质...");
    
    try {
        // 创建一个最简单的 unlit 材质源码
        const char* simpleUnlitSource = R"(
            material {
                name : RuntimeUnlit,
                shadingModel : unlit,
                blending : opaque,
                parameters : [
                    {
                        type : float3,
                        name : baseColor
                    }
                ]
            }
            
            fragment {
                void material(inout MaterialInputs material) {
                    prepareMaterial(material);
                    material.baseColor.rgb = materialParams.baseColor;
                    material.baseColor.a = 1.0;
                }
            }
        )";
        
        // 注意：这个方法在没有材质编译器的情况下不会工作
        // 但我们可以尝试其他方法
        
        NSLog(@"⚠️ 运行时材质编译需要额外工具，跳转到顶点颜色方案");
        return nullptr;
        
    } catch (...) {
        NSLog(@"❌ 无法创建运行时材质");
        return nullptr;
    }
}

// 动态调整相机距离
- (void)setCameraDistance:(float)distance {
    if (!_engine || _cameraEntity.isNull()) return;
    
    auto& tcm = _engine->getTransformManager();
    math::float3 eye = {0, 0, distance};
    math::float3 center = {0, 0, 0};
    math::float3 up = {0, 1, 0};
    
    math::float3 zAxis = normalize(eye - center);
    math::float3 xAxis = normalize(cross(up, zAxis));
    math::float3 yAxis = cross(zAxis, xAxis);
    
    math::mat4f transform = {
        math::float4(xAxis, 0),
        math::float4(yAxis, 0),
        math::float4(zAxis, 0),
        math::float4(eye, 1)
    };
    
    tcm.setTransform(tcm.getInstance(_cameraEntity), transform);
    
    // 计算并输出视野信息
    double halfFovRad = (45.0 * M_PI / 180.0) / 2.0;
    double visibleRadius = tan(halfFovRad) * distance;
    NSLog(@"相机距离已调整为: %.1f, 可见半径: %.2f", distance, visibleRadius);
}

// 尝试应用纹理到材质
- (BOOL)tryApplyTextureToMaterial:(MaterialInstance*)material texture:(Texture*)texture {
    if (!material || !texture) return NO;
    
    const Material* mat = material->getMaterial();
    
    // 创建合适的纹理采样器
    TextureSampler sampler = TextureSampler(
        TextureSampler::MinFilter::LINEAR,
        TextureSampler::MagFilter::LINEAR,
        TextureSampler::WrapMode::REPEAT
    );
    
    // 尝试常见的纹理参数名称（按优先级排序）
    NSArray* textureParams = @[
        @"baseColorMap",      // PBR 材质的主要纹理参数
        @"diffuseMap",        // 传统材质的漫反射纹理
        @"albedoMap",         // 另一种常见的颜色纹理名称
        @"colorMap",          // 通用颜色纹理
        @"texture",           // 最基本的纹理参数
        @"mainTexture",       // Unity 风格的主纹理
        @"diffuse"            // 简化的漫反射参数
    ];
    
    NSMutableArray* availableParams = [NSMutableArray array];
    NSMutableArray* attemptedParams = [NSMutableArray array];
    
    // 首先检查所有可用的纹理参数
    for (NSString* paramName in textureParams) {
        const char* cParamName = [paramName UTF8String];
        if (mat->hasParameter(cParamName)) {
            [availableParams addObject:paramName];
        }
    }
    
    NSLog(@"🔍 材质支持的纹理参数: %@", availableParams);
    
    if (availableParams.count > 0) {
        // 尝试设置每个可用的纹理参数
        for (NSString* paramName in availableParams) {
            const char* cParamName = [paramName UTF8String];
            [attemptedParams addObject:paramName];
            
            try {
                material->setParameter(cParamName, texture, sampler);
                NSLog(@"✅ 成功设置纹理参数: %@", paramName);
                return YES;
            } catch (const std::exception& e) {
                NSLog(@"❌ 设置纹理参数 %@ 时发生异常: %s", paramName, e.what());
            } catch (...) {
                NSLog(@"❌ 设置纹理参数 %@ 时发生未知异常", paramName);
            }
        }
        
        NSLog(@"⚠️ 所有纹理参数设置尝试都失败了，尝试的参数: %@", attemptedParams);
    } else {
        NSLog(@"⚠️ 材质不支持任何纹理参数，将使用颜色方案模拟纹理");
    }
    
    // 如果不能直接应用纹理，我们尝试从纹理中提取主要颜色
    return [self simulateTextureWithColor:material textureIndex:_activeTextureIndex];
}

// 通过顶点颜色模拟纹理效果
- (BOOL)simulateTextureWithColor:(MaterialInstance*)material textureIndex:(int)textureIndex {
    NSLog(@"🎨 使用顶点颜色模拟纹理效果 (纹理索引: %d)", textureIndex);
    
    // 定义一些基于纹理内容的代表性颜色
    static const math::float4 textureColors[] = {
        {0.3f, 0.5f, 0.9f, 1.0f}, // 天蓝色 - 默认蓝色纹理
        {0.2f, 0.7f, 0.3f, 1.0f}, // 翠绿色 - 模拟草地纹理
        {0.8f, 0.4f, 0.2f, 1.0f}, // 温暖的棕色 - 模拟木纹纹理
        {0.9f, 0.8f, 0.3f, 1.0f}, // 金黄色 - 模拟金属纹理
        {0.6f, 0.3f, 0.8f, 1.0f}, // 紫色 - 模拟神秘纹理
        {0.9f, 0.5f, 0.6f, 1.0f}, // 粉红色 - 模拟花朵纹理
        {0.5f, 0.5f, 0.5f, 1.0f}, // 银灰色 - 模拟金属纹理
        {0.9f, 0.6f, 0.2f, 1.0f}, // 橙色 - 模拟火焰纹理
    };
    
    int colorCount = sizeof(textureColors) / sizeof(textureColors[0]);
    int colorIndex = textureIndex % colorCount;
    math::float4 simulatedColor = textureColors[colorIndex];
    
    // 更新球体顶点颜色
    return [self updateSphereVertexColor:simulatedColor];
}

// 更新球体顶点颜色
- (BOOL)updateSphereVertexColor:(math::float4)color {
    if (!_sphereVertexBuffer || _sphereVertices.empty()) {
        NSLog(@"❌ 球体顶点缓冲区或数据不存在");
        return NO;
    }
    
    NSLog(@"🌈 更新球体顶点颜色: (%.2f, %.2f, %.2f, %.2f)", color.r, color.g, color.b, color.a);
    
    // 更新所有顶点的颜色
    for (size_t i = 0; i < _sphereVertices.size(); i++) {
        _sphereVertices[i].color = color;
    }
    
    try {
        // 重新上传顶点数据到 GPU
        _sphereVertexBuffer->setBufferAt(*_engine, 0,
            VertexBuffer::BufferDescriptor(_sphereVertices.data(), 
                                         _sphereVertices.size() * sizeof(SphereVertex)));
        
        NSLog(@"✅ 球体顶点颜色更新成功，共更新 %zu 个顶点", _sphereVertices.size());
        return YES;
        
    } catch (const std::exception& e) {
        NSLog(@"❌ 更新顶点颜色时发生异常: %s", e.what());
        return NO;
    } catch (...) {
        NSLog(@"❌ 更新顶点颜色时发生未知异常");
        return NO;
    }
}

// 根据纹理索引设置材质颜色（简化版本，现在主要调用 simulateTextureWithColor）
- (void)setMaterialColorForTextureIndex:(int)index material:(MaterialInstance*)material {
    if (!material) return;
    
    NSLog(@"🔄 使用简化颜色设置方法 (索引: %d)", index);
    
    // 直接调用我们改进的纹理模拟方法
    BOOL success = [self simulateTextureWithColor:material textureIndex:index];
    
    if (!success) {
        NSLog(@"⚠️ 纹理颜色模拟失败，尝试基础颜色设置");
        
        // 降级方案：使用简单的颜色
        static const math::float3 fallbackColors[] = {
            {0.8f, 0.2f, 0.2f}, // 红色
            {0.2f, 0.8f, 0.2f}, // 绿色
            {0.2f, 0.2f, 0.8f}, // 蓝色
            {0.8f, 0.8f, 0.2f}, // 黄色
        };
        
        int colorCount = sizeof(fallbackColors) / sizeof(fallbackColors[0]);
        int colorIndex = index % colorCount;
        math::float3 fallbackColor = fallbackColors[colorIndex];
        
        const Material* mat = material->getMaterial();
        
        if (mat->hasParameter("baseColor")) {
            try {
                material->setParameter("baseColor", RgbType::sRGB, fallbackColor);
                NSLog(@"✅ 设置降级颜色 baseColor: (%.2f, %.2f, %.2f)", 
                      fallbackColor.r, fallbackColor.g, fallbackColor.b);
            } catch (...) {
                NSLog(@"❌ 降级颜色设置也失败了");
            }
        }
    }
}

#pragma mark - 纹理管理

- (BOOL)loadTextureFromUIImage:(UIImage *)image {
    if (!_engine || !image || !image.CGImage) {
        return NO;
    }
    
    CGImageRef cgImage = image.CGImage;
    size_t width = CGImageGetWidth(cgImage);
    size_t height = CGImageGetHeight(cgImage);
    
    NSLog(@"加载纹理：%zux%zu", width, height);
    
    // Filament 推荐的纹理格式和大小
    size_t bytesPerPixel = 4; // RGBA
    size_t bytesPerRow = width * bytesPerPixel;
    size_t totalBytes = height * bytesPerRow;
    
    // 创建像素数据缓冲
    std::vector<uint8_t> pixelData(totalBytes);
    
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    CGContextRef context = CGBitmapContextCreate(
        pixelData.data(),
        width, height,
        8, bytesPerRow,
        colorSpace,
        kCGImageAlphaPremultipliedLast | kCGBitmapByteOrder32Big
    );
    
    if (!context) {
        CGColorSpaceRelease(colorSpace);
        return NO;
    }
    
    CGContextDrawImage(context, CGRectMake(0, 0, width, height), cgImage);
    CGContextRelease(context);
    CGColorSpaceRelease(colorSpace);
    
    // 创建 Filament 纹理
    Texture* texture = Texture::Builder()
        .width((uint32_t)width)
        .height((uint32_t)height)
        .levels(1)
        .format(Texture::InternalFormat::RGBA8)
        .build(*_engine);
    
    if (!texture) {
        return NO;
    }
    
    // 上传纹理数据
    backend::PixelBufferDescriptor buffer(
        pixelData.data(),
        totalBytes,
        backend::PixelDataFormat::RGBA,
        backend::PixelDataType::UBYTE
    );
    
    texture->setImage(*_engine, 0, std::move(buffer));
    
    // 添加到纹理列表
    _textures.push_back(texture);
    
    return YES;
}

- (void)clearTextures {
    if (_engine) {
        for (Texture* texture : _textures) {
            _engine->destroy(texture);
        }
    }
    _textures.clear();
    _activeTextureIndex = 0;
}

- (BOOL)selectTextureAtIndex:(NSInteger)index {
    if (index < 0 || index >= (NSInteger)_textures.size()) {
        return NO;
    }
    
    _activeTextureIndex = (int)index;
    [self updateMaterialTexture];
    return YES;
}

- (NSInteger)getTextureCount {
    return (NSInteger)_textures.size();
}

- (void)updateMaterialTexture {
    if (_textures.empty() || _activeTextureIndex >= (int)_textures.size() || 
        _modelEntity.isNull() || !_engine) {
        NSLog(@"⚠️ 无法更新材质纹理：纹理列表为空(%zu)，索引(%d)，或实体无效(%d)", 
              _textures.size(), _activeTextureIndex, _modelEntity.getId());
        return;
    }
    
    // 获取当前材质实例
    MaterialInstance* currentMaterial = nullptr;
    if (_sphereMaterial) {
        currentMaterial = _sphereMaterial;
        NSLog(@"使用球体材质");
    } else if (_currentMaterial) {
        currentMaterial = _currentMaterial;
        NSLog(@"使用当前材质");
    }
    
    if (!currentMaterial) {
        NSLog(@"❌ 没有可用的材质实例");
        return;
    }
    
    Texture* activeTexture = _textures[_activeTextureIndex];
    if (!activeTexture) {
        NSLog(@"❌ 活动纹理为空，索引: %d", _activeTextureIndex);
        return;
    }
    
    // 获取材质信息
    const Material* material = currentMaterial->getMaterial();
    NSLog(@"🎨 开始更新材质纹理");
    NSLog(@"   材质名称: %s", material->getName());
    NSLog(@"   纹理索引: %d/%zu", _activeTextureIndex, _textures.size());
    
    // 方案1：尝试直接应用纹理
    BOOL textureApplied = [self tryApplyTextureToMaterial:currentMaterial texture:activeTexture];
    
    if (textureApplied) {
        NSLog(@"✅ 成功应用纹理到材质");
        
        // 纹理应用成功后，确保其他参数也合适
        if (material->hasParameter("baseColorFactor")) {
            try {
                currentMaterial->setParameter("baseColorFactor", 1.0f);
                NSLog(@"设置 baseColorFactor = 1.0");
            } catch (...) {}
        }
        
    } else {
        // 方案2：使用颜色变化表示不同纹理
        NSLog(@"⚠️ 材质不支持纹理参数，使用颜色变化表示纹理加载");
        [self setMaterialColorForTextureIndex:_activeTextureIndex material:currentMaterial];
    }
    
    NSLog(@"🎨 材质纹理更新完成，索引: %d", _activeTextureIndex);
    
    // 强制引擎更新渲染状态
    // 注意：Filament 通常会自动处理这些更新，但我们可以显式通知
    if (_scene && !_modelEntity.isNull()) {
        // 重新添加实体到场景以触发更新（如果需要）
        // _scene->remove(_modelEntity);
        // _scene->addEntity(_modelEntity);
        NSLog(@"场景中实体状态正常");
    }
}

#pragma mark - 纹理变换

- (void)moveTextureWithDeltaX:(float)deltaX deltaY:(float)deltaY {
    _textureOffset.x += deltaX;
    _textureOffset.y += deltaY;
    [self updateMaterialTexture];
}

- (void)resetTexturePosition {
    _textureOffset = {0.0f, 0.0f};
    [self updateMaterialTexture];
}

#pragma mark - 动画控制

- (void)startTextureAnimationWithSpeed:(float)speed {
    [self stopTextureAnimation];
    
    _animationTimer = [NSTimer scheduledTimerWithTimeInterval:1.0/60.0
                                                      repeats:YES
                                                        block:^(NSTimer * _Nonnull timer) {
        [self moveTextureWithDeltaX:speed deltaY:0.0f];
    }];
}

- (void)stopTextureAnimation {
    if (_animationTimer) {
        [_animationTimer invalidate];
        _animationTimer = nil;
    }
}

@end
