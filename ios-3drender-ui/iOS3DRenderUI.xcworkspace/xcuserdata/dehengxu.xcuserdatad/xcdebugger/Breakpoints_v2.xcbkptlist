<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "B2F10100-5032-4631-A085-67F43C12F8CA"
   type = "0"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B0009462-8E46-4BF5-BCD6-2950B1CE6654"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "iOS3DRenderUI/MainViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "323"
            endingLineNumber = "323"
            landmarkName = "picker(_:didFinishPicking:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "B0009462-8E46-4BF5-BCD6-2950B1CE6654 - 9cd57ed2c1608c81"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 @Swift.MainActor () -&gt; () in closure #1 @Sendable (Swift.Optional&lt;__C.NSItemProviderReading&gt;, Swift.Optional&lt;Swift.Error&gt;) -&gt; () in iOS3DRenderUI.MainViewController.picker(_: __C.PHPickerViewController, didFinishPicking: Swift.Array&lt;PhotosUI.PHPickerResult&gt;) -&gt; ()"
                  moduleName = "iOS3DRenderUI.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Projects/personal/Filamenet-1/ios-3drender-ui/iOS3DRenderUI/MainViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "323"
                  endingLineNumber = "323">
               </Location>
               <Location
                  uuid = "B0009462-8E46-4BF5-BCD6-2950B1CE6654 - d8a9832d21f1ce4f"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Swift.Bool) -&gt; () in closure #1 @Swift.MainActor () -&gt; () in closure #1 @Sendable (Swift.Optional&lt;__C.NSItemProviderReading&gt;, Swift.Optional&lt;Swift.Error&gt;) -&gt; () in iOS3DRenderUI.MainViewController.picker(_: __C.PHPickerViewController, didFinishPicking: Swift.Array&lt;PhotosUI.PHPickerResult&gt;) -&gt; ()"
                  moduleName = "iOS3DRenderUI.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Projects/personal/Filamenet-1/ios-3drender-ui/iOS3DRenderUI/MainViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "316"
                  endingLineNumber = "316">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "5721D69F-3385-4C69-AE47-7C7F9AF31A18"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "iOS3DRenderUI/MainViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "324"
            endingLineNumber = "324"
            landmarkName = "picker(_:didFinishPicking:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
